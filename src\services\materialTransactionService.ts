/**
 * Material Transaction Service
 * 
 * Handles material issue and receipt operations with proper validation
 * Ensures customer segregation and accurate loss tracking
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';

type MaterialTransaction = Database['public']['Tables']['material_transactions']['Row'];
type MaterialTransactionInsert = Database['public']['Tables']['material_transactions']['Insert'];

export interface MaterialIssueRequest {
  order_id: string;
  worker_id: string;
  process_id: string;
  customer_id: string;
  material_type: 'stone' | 'finding' | 'metal';
  issued_by: string;
  notes?: string;

  // Metal-specific properties
  metal_type_id?: string;
  karat_id?: string;
  weight_grams?: number;
  expected_loss_percentage?: number;

  // Stone-specific properties
  stone_type_id?: string;
  stone_shape_id?: string;
  stone_size_id?: string;
  stone_quality_id?: string;
  quantity?: number;
  total_weight_carats?: number;

  // Finding-specific properties
  finding_id?: string;
}

export interface MaterialReceiptRequest {
  transaction_id: string;
  received_weight_grams: number;
  dust_collected_grams?: number;
  loss_percentage: number;
  received_by: string;
  notes?: string;
}

export interface LossCalculationResult {
  actualLossPercentage: number;
  expectedLossPercentage: number;
  isWithinTolerance: boolean;
  lossAmount: number;
}

/**
 * Issue material to worker for processing
 */
export async function issueMaterialToWorker(
  request: MaterialIssueRequest
): Promise<MaterialTransaction> {
  try {
    // 1. Validate customer segregation
    await validateCustomerSegregation(request.customer_id, request.order_id);
    
    // 2. Create transaction record
    const transactionData: MaterialTransactionInsert = {
      order_id: request.order_id,
      worker_id: request.worker_id,
      process_id: request.process_id,
      customer_id: request.customer_id,
      transaction_type: 'issue',
      material_type: request.material_type,
      transaction_date: new Date().toISOString(),
      issued_by: request.issued_by,
      status: 'issued',
      notes: request.notes,

      // Add material-specific properties if provided
      issued_weight_grams: request.weight_grams,
      expected_loss_percentage: request.expected_loss_percentage
    };

    const { data, error } = await supabase
      .from('material_transactions')
      .insert([transactionData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error issuing material:', error);
    throw error;
  }
}

/**
 * Receive material back from worker
 */
export async function receiveMaterialFromWorker(
  request: MaterialReceiptRequest
): Promise<MaterialTransaction> {
  try {
    // 1. Get original transaction
    const { data: transaction, error: fetchError } = await supabase
      .from('material_transactions')
      .select('*')
      .eq('transaction_id', request.transaction_id)
      .single();

    if (fetchError) throw fetchError;

    // 2. Calculate loss
    const lossData = calculateLoss(
      transaction.issued_weight_grams || 0,
      request.received_weight_grams,
      transaction.process_id
    );

    // 3. Update transaction
    const { data, error } = await supabase
      .from('material_transactions')
      .update({
        received_weight_grams: request.received_weight_grams,
        dust_collected_grams: request.dust_collected_grams,
        loss_percentage: lossData.actualLossPercentage,
        expected_loss_percentage: lossData.expectedLossPercentage,
        received_date: new Date().toISOString(),
        received_by: request.received_by,
        status: 'completed',
        notes: request.notes
      })
      .eq('transaction_id', request.transaction_id)
      .select()
      .single();

    if (error) throw error;

    // 4. Create dust parcel if dust collected
    if (request.dust_collected_grams && request.dust_collected_grams > 0) {
      await createDustParcel({
        transaction_id: request.transaction_id,
        weight_grams: request.dust_collected_grams,
        worker_id: transaction.worker_id,
        process_id: transaction.process_id,
        customer_id: transaction.customer_id
      });
    }

    return data;
  } catch (error) {
    console.error('Error receiving material:', error);
    throw error;
  }
}

/**
 * Calculate loss based on issued vs received weights
 */
export function calculateLoss(
  issuedWeight: number,
  receivedWeight: number,
  processId: string
): LossCalculationResult {
  const lossAmount = issuedWeight - receivedWeight;
  const actualLossPercentage = (lossAmount / issuedWeight) * 100;
  
  // Get expected loss percentage based on process
  const expectedLossPercentage = getExpectedLossPercentage(processId);
  
  // Allow 0.5% tolerance
  const tolerance = 0.5;
  const isWithinTolerance = Math.abs(actualLossPercentage - expectedLossPercentage) <= tolerance;

  return {
    actualLossPercentage,
    expectedLossPercentage,
    isWithinTolerance,
    lossAmount
  };
}

/**
 * Create dust parcel for collected dust
 */
export async function createDustParcel({
  transaction_id,
  weight_grams,
  worker_id,
  process_id,
  customer_id
}: {
  transaction_id: string;
  weight_grams: number;
  worker_id: string;
  process_id: string;
  customer_id: string;
}) {
  try {
    const dustParcelData = {
      transaction_id,
      weight_grams,
      worker_id,
      process_id,
      customer_id,
      collection_date: new Date().toISOString(),
      status: 'collected',
      purity_estimate: 0.22, // Default 22kt for gold dust
      expected_recovery_rate: 0.8 // 80% recovery rate
    };

    const { data, error } = await supabase
      .from('dust_parcels_enhanced')
      .insert([dustParcelData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating dust parcel:', error);
    throw error;
  }
}

/**
 * Validate customer segregation - ensure materials don't mix
 */
export async function validateCustomerSegregation(
  customerId: string,
  orderId: string
): Promise<void> {
  try {
    // Verify order belongs to the specified customer
    const { data: order, error } = await supabase
      .from('orders_mast')
      .select('customer_id')
      .eq('order_id', orderId)
      .single();

    if (error) throw error;

    if (order.customer_id !== customerId) {
      throw new Error('Order does not belong to specified customer - segregation violation');
    }
  } catch (error) {
    console.error('Customer segregation validation failed:', error);
    throw error;
  }
}

/**
 * Get expected loss percentage based on process
 */
function getExpectedLossPercentage(processId: string): number {
  // Default loss percentages by process (from memory)
  const processLossRates: Record<string, number> = {
    'filing': 2.5,
    'setting': 1.5,
    'polishing': 0.4, // 2% with 80% recovery
    'pre-polishing': 0.0, // No dust generated
    'casting': 1.0,
    'default': 1.0
  };

  // For now, return default. In production, this would query process_mast
  return processLossRates['default'];
}

/**
 * Get material transactions by order
 */
export async function getMaterialTransactionsByOrder(orderId: string) {
  try {
    const { data, error } = await supabase
      .from('material_transactions')
      .select(`
        *,
        worker:workers_mast(worker_name),
        process:process_mast(process_name)
      `)
      .eq('order_id', orderId)
      .order('transaction_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching material transactions:', error);
    throw error;
  }
}

/**
 * Get pending material issues (issued but not received)
 */
export async function getPendingMaterialIssues() {
  try {
    const { data, error } = await supabase
      .from('material_transactions')
      .select(`
        *,
        worker:workers_mast(worker_name),
        process:process_mast(process_name),
        order:orders_mast(order_no, customer:customers_mast(customer_name))
      `)
      .eq('status', 'issued')
      .order('transaction_date', { ascending: true });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching pending material issues:', error);
    throw error;
  }
}

// Aliases for component compatibility
export const issueMaterial = issueMaterialToWorker;
export const receiveMaterial = receiveMaterialFromWorker;

/**
 * Get issued materials for receipt (alias for getMaterialTransactionsByStatus)
 */
export async function getIssuedMaterials(filters: {
  customer_id?: string;
  order_id?: string;
  worker_id?: string;
  process_id?: string;
  material_type?: string;
}) {
  try {
    let query = supabase
      .from('material_transactions')
      .select(`
        *,
        metal_type:metal_types_mast(metal_name),
        karat:karat_mast(karat_name, purity_percentage),
        worker:workers_mast(name),
        process:process_mast(name)
      `)
      .eq('status', 'issued');

    if (filters.customer_id) query = query.eq('customer_id', filters.customer_id);
    if (filters.order_id) query = query.eq('order_id', filters.order_id);
    if (filters.worker_id) query = query.eq('worker_id', filters.worker_id);
    if (filters.process_id) query = query.eq('process_id', filters.process_id);
    if (filters.material_type) query = query.eq('material_type', filters.material_type);

    const { data, error } = await query.order('issue_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching issued materials:', error);
    throw error;
  }
}
