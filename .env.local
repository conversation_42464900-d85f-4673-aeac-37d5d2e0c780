# Supabase Configuration
# Get these values from your Supabase project dashboard at https://supabase.com/dashboard

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key (safe to use in client-side code)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Your Supabase service role key (keep this secret, server-side only)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database URL (for direct database connections if needed)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres

# Optional: JWT Secret (usually not needed for standard Supabase usage)
# SUPABASE_JWT_SECRET=your-jwt-secret-here

# Optional: Additional environment variables you might need
# NEXTAUTH_SECRET=your-nextauth-secret-here
# NEXTAUTH_URL=http://localhost:3000