# Supabase Configuration
# Get these values from your Supabase project dashboard at https://supabase.com/dashboard
supabase:
  url: https://vcimuvdnftocekbqrbfy.supabase.co
  anonKey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6ImFub24iLCJpY

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://vcimuvdnftocekbqrbfy.supabase.co

# Your Supabase anon/public key (safe to use in client-side code)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6ImFub24iLCJpY

# Your Supabase service role key (keep this secret, server-side only)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTA1MDg2MywiZXhwIjoyMDUwNjI2ODYzfQ.yf0J4tTvvQwFshu8uui7cdYPToPg0d409rQjaDlpCF0

# Database URL (for direct database connections if needed)
DATABASE_URL=postgresql://postgres:[Labhnivas33]@db.your-project-id.supabase.co:5432/postgres
postgresql://postgres:[YOUR-PASSWORD]@db.vcimuvdnftocekbqrbfy.supabase.co:5432/postgres
# Optional: JWT Secret (usually not needed for standard Supabase usage)
# SUPABASE_JWT_SECRET=your-jwt-secret-here

# Optional: Additional environment variables you might need
# NEXTAUTH_SECRET=your-nextauth-secret-here
# NEXTAUTH_URL=http://localhost:3000