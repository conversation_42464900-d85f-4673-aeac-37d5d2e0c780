/**
 * Inventory Masters Page
 * Displays different master data tables based on the view query parameter
 * 
 * @module app/inventory/masters
 */

'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import PageHeader from '@/components/common/PageHeader';
import { MetalTypeMaster } from '@/components/masters/MetalTypeMaster';
import { StoneTypeMaster } from '@/components/masters/StoneTypeMaster';
import { StoneShapeMaster } from '@/components/masters/StoneShapeMaster';

/**
 * Inventory Masters Page Component
 * Displays different master data tables based on the view query parameter
 */
export default function InventoryMastersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const view = searchParams.get('view') || 'metal-types';
  
  // Render the appropriate component based on the view parameter
  const renderView = () => {
    switch (view) {
      case 'metal-types':
        return <MetalTypeMaster />;
      case 'stone-types':
        return <StoneTypeMaster />;
      case 'stone-shapes':
        return <StoneShapeMaster />;
      default:
        return <MetalTypeMaster />;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <PageHeader 
        title="Inventory Masters" 
        description="Manage inventory master data including metal types, stone types, and more"
      />
      
      <div className="flex border-b mb-6 mt-4">
        <button 
          className={`px-4 py-2 ${view === 'metal-types' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500'}`}
          onClick={() => router.push('/masters/inventory?view=metal-types')}
        >
          Metal Types
        </button>
        <button 
          className={`px-4 py-2 ${view === 'stone-types' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500'}`}
          onClick={() => router.push('/masters/inventory?view=stone-types')}
        >
          Stone Types
        </button>
        <button 
          className={`px-4 py-2 ${view === 'stone-shapes' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500'}`}
          onClick={() => router.push('/masters/inventory?view=stone-shapes')}
        >
          Stone Shapes
        </button>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-md shadow p-6">
        {renderView()}
      </div>
    </div>
  );
}
