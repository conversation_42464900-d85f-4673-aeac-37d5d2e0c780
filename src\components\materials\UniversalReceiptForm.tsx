/**
 * @module components/materials/UniversalReceiptForm
 * @description Universal Material Receipt Form - Unified interface for receiving all material types
 * 
 * UX Features:
 * - Single form for stones, findings, and metals
 * - Smart context detection and auto-population
 * - Batch processing with real-time calculations
 * - Visual loss tracking with color-coded feedback
 * - Integrated dust collection workflow
 */

'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  AlertTriangleIcon, 
  CheckCircleIcon, 
  UserIcon, 
  PackageIcon,
  ScaleIcon,
  GemIcon,
  CircleIcon,
  ClipboardCheckIcon,
  InfoIcon
} from 'lucide-react';

// Import existing services
import { getIssuedStones, receiveStones } from '@/services/stoneInventoryService';
import { getIssuedFindings, receiveFindings } from '@/services/findingsInventoryService';
import { getIssuedMaterials, receiveMaterial } from '@/services/materialTransactionService';

// Import types
import { Order } from '@/types/database';

// Import components
import { MaterialItemRow } from './MaterialItemRow';

// Schema for form validation
const universalReceiptSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().optional(),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  notes: z.string().optional()
});

interface UniversalReceiptFormData {
  customer_id: string;
  order_id?: string;
  worker_id: string;
  process_id: string;
  notes?: string;
}

interface MaterialItem {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  issuedWeight: number;
  issuedQuantity?: number;
  receivedWeight: number;
  receivedQuantity?: number;
  dustCollected: number;
  lossPercentage: number;
  status: 'pending' | 'completed' | 'flagged';
  originalData: any; // Store original transaction data
}

interface UniversalReceiptFormProps {
  workerId?: string;
  processId?: string;
  customerId?: string;
  onSuccess?: () => void;
}

export function UniversalReceiptForm({ 
  workerId, 
  processId, 
  customerId,
  onSuccess 
}: UniversalReceiptFormProps) {
  // Form state
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Material state
  const [materialItems, setMaterialItems] = useState<MaterialItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showBatchActions, setShowBatchActions] = useState(false);

  // Context state
  const [selectedCustomerId, setSelectedCustomerId] = useState(customerId || '');
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [selectedWorkerId, setSelectedWorkerId] = useState(workerId || '');
  const [selectedProcessId, setSelectedProcessId] = useState(processId || '');

  const form = useForm<UniversalReceiptFormData>({
    resolver: zodResolver(universalReceiptSchema),
    defaultValues: {
      customer_id: customerId || '',
      order_id: '',
      worker_id: workerId || '',
      process_id: processId || '',
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load issued materials when context changes
  useEffect(() => {
    if (selectedCustomerId && selectedWorkerId && selectedProcessId) {
      loadIssuedMaterials();
    }
  }, [selectedCustomerId, selectedOrderId, selectedWorkerId, selectedProcessId]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalItems = materialItems.length;
    const completedItems = materialItems.filter(item => item.status === 'completed').length;
    const flaggedItems = materialItems.filter(item => item.status === 'flagged').length;
    const totalLoss = materialItems.reduce((sum, item) => sum + item.lossPercentage, 0) / totalItems || 0;
    const totalDust = materialItems.reduce((sum, item) => sum + item.dustCollected, 0);

    return {
      totalItems,
      completedItems,
      flaggedItems,
      averageLoss: totalLoss,
      totalDust
    };
  }, [materialItems]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json())
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadIssuedMaterials = async () => {
    try {
      setIsLoading(true);
      
      // Load all material types in parallel
      const [stones, findings, metals] = await Promise.all([
        getIssuedStones({
          customer_id: selectedCustomerId,
          order_id: selectedOrderId || undefined,
          worker_id: selectedWorkerId,
          process_id: selectedProcessId
        }),
        getIssuedFindings({
          customer_id: selectedCustomerId,
          order_id: selectedOrderId || undefined,
          worker_id: selectedWorkerId,
          process_id: selectedProcessId
        }),
        getIssuedMaterials({
          customer_id: selectedCustomerId,
          order_id: selectedOrderId || undefined,
          worker_id: selectedWorkerId,
          process_id: selectedProcessId,
          material_type: 'metal'
        })
      ]);

      // Transform data into unified format
      const unifiedItems: MaterialItem[] = [
        ...stones.map(stone => transformStoneToMaterialItem(stone)),
        ...findings.map(finding => transformFindingToMaterialItem(finding)),
        ...metals.map(metal => transformMetalToMaterialItem(metal))
      ];

      setMaterialItems(unifiedItems);
      
      if (unifiedItems.length === 0) {
        toast.info('No issued materials found for the selected criteria');
      }

    } catch (error) {
      console.error('Error loading issued materials:', error);
      toast.error('Failed to load issued materials');
    } finally {
      setIsLoading(false);
    }
  };

  // Transform functions to unify different material types
  const transformStoneToMaterialItem = (stone: any): MaterialItem => ({
    id: stone.stone_transaction_id,
    type: 'stone',
    description: `${stone.quantity_issued} pcs ${stone.stone_details?.type_name || 'Stone'} - ${stone.stone_shape?.shape_name || ''} - ${stone.stone_size?.size_name || ''}`,
    issuedWeight: stone.weight_carats_issued,
    issuedQuantity: stone.quantity_issued,
    receivedWeight: stone.weight_carats_issued, // Default to issued weight
    receivedQuantity: stone.quantity_issued, // Default to issued quantity
    dustCollected: 0,
    lossPercentage: 0,
    status: 'pending',
    originalData: stone
  });

  const transformFindingToMaterialItem = (finding: any): MaterialItem => ({
    id: finding.finding_transaction_id,
    type: 'finding',
    description: `${finding.finding?.finding_type || 'Finding'} - ${finding.gross_weight_grams}g`,
    issuedWeight: finding.gross_weight_grams,
    receivedWeight: finding.gross_weight_grams, // Default to issued weight
    dustCollected: 0,
    lossPercentage: 0,
    status: 'pending',
    originalData: finding
  });

  const transformMetalToMaterialItem = (metal: any): MaterialItem => ({
    id: metal.transaction_id,
    type: 'metal',
    description: `${metal.weight_grams}g ${metal.metal_type?.metal_name || 'Metal'} - ${metal.karat?.karat_name || ''}`,
    issuedWeight: metal.weight_grams,
    receivedWeight: metal.weight_grams, // Default to issued weight
    dustCollected: 0,
    lossPercentage: 0,
    status: 'pending',
    originalData: metal
  });

  return (
    <div className="space-y-6">
      {/* Context Bar */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <ClipboardCheckIcon className="w-5 h-5" />
            Universal Material Receipt
          </CardTitle>
          <CardDescription>
            Receive all material types in one unified workflow with intelligent batch processing
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Order-Centric Context Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Order Receipt Context</CardTitle>
          <CardDescription>
            Select order and process to load materials issued to worker for receipt and loss tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Order Selection - PRIMARY */}
            <div className="space-y-2">
              <Label htmlFor="order_id" className="text-base font-semibold text-blue-700">Order # *</Label>
              <select
                value={selectedOrderId}
                onChange={(e) => {
                  const value = e.target.value;
                  setSelectedOrderId(value);
                  form.setValue('order_id', value);
                  // Auto-load customer when order is selected
                  const order = orders.find(o => o.order_id === value);
                  if (order) {
                    setSelectedCustomerId(order.customer_id);
                    form.setValue('customer_id', order.customer_id);
                  }
                }}
                className="w-full h-10 px-3 py-2 border-2 border-blue-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select order number</option>
                {orders.map((order: any) => (
                  <option key={order.order_id} value={order.order_id}>
                    {order.order_no} - {order.customer_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Process Selection - SECONDARY */}
            <div className="space-y-2">
              <Label htmlFor="process_id" className="text-base font-semibold text-green-700">Process *</Label>
              <select
                value={selectedWorkerId}
                onChange={(e) => {
                  const value = e.target.value;
                  setSelectedWorkerId(value);
                  form.setValue('worker_id', value);
                }}
                className="w-full h-10 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select worker</option>
                {workers.map((worker: any) => (
                  <option key={worker.worker_id} value={worker.worker_id}>
                    {worker.worker_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Process Selection */}
            <div className="space-y-2">
              <Label htmlFor="process_id">Process *</Label>
              <select
                value={selectedProcessId}
                onChange={(e) => {
                  const value = e.target.value;
                  setSelectedProcessId(value);
                  form.setValue('process_id', value);
                }}
                className="w-full h-10 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">Select process</option>
                {processes.map((process: any) => (
                  <option key={process.process_id} value={process.process_id}>
                    {process.process_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Order Selection (Optional) */}
            <div className="space-y-2">
              <Label htmlFor="order_id">Order (Optional)</Label>
              <select
                value={selectedOrderId}
                onChange={(e) => {
                  const value = e.target.value;
                  setSelectedOrderId(value);
                  form.setValue('order_id', value);
                }}
                className="w-full h-10 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All orders</option>
                {orders.map((order: any) => (
                  <option key={order.order_id} value={order.order_id}>
                    {order.order_no}
                  </option>
                ))}
              </select>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      {materialItems.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{summaryStats.totalItems}</div>
              <div className="text-sm text-gray-600">Total Items</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{summaryStats.completedItems}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{summaryStats.flaggedItems}</div>
              <div className="text-sm text-gray-600">Flagged</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{summaryStats.averageLoss.toFixed(2)}%</div>
              <div className="text-sm text-gray-600">Avg Loss</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{summaryStats.totalDust.toFixed(2)}g</div>
              <div className="text-sm text-gray-600">Total Dust</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading issued materials...</p>
          </CardContent>
        </Card>
      )}

      {/* Material Items Table */}
      {materialItems.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <PackageIcon className="w-5 h-5" />
                  Issued Materials for Receipt
                </CardTitle>
                <CardDescription>
                  Select items and enter received weights to complete receipt
                </CardDescription>
              </div>
              {selectedItems.length > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {selectedItems.length} selected
                  </Badge>
                  <Button
                    size="sm"
                    onClick={() => setShowBatchActions(!showBatchActions)}
                  >
                    Batch Actions
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Batch Actions */}
              {showBatchActions && selectedItems.length > 0 && (
                <Card className="bg-blue-50 dark:bg-blue-950">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <Button
                        size="sm"
                        onClick={() => handleBatchReceiptAsIssued()}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Receive All As Issued
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleBatchDustCollection()}
                      >
                        Add Dust Collection
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedItems([])}
                      >
                        Clear Selection
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Material Items Table */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedItems.length === materialItems.length}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedItems(materialItems.map(item => item.id));
                          } else {
                            setSelectedItems([]);
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Issued</TableHead>
                    <TableHead>Received</TableHead>
                    <TableHead>Dust</TableHead>
                    <TableHead>Loss %</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {materialItems.map((item) => (
                    <MaterialItemRow
                      key={item.id}
                      item={item}
                      isSelected={selectedItems.includes(item.id)}
                      onSelectionChange={(selected) => {
                        if (selected) {
                          setSelectedItems([...selectedItems, item.id]);
                        } else {
                          setSelectedItems(selectedItems.filter(id => id !== item.id));
                        }
                      }}
                      onItemUpdate={(updatedItem) => {
                        setMaterialItems(items =>
                          items.map(i => i.id === updatedItem.id ? updatedItem : i)
                        );
                      }}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Materials Message */}
      {!isLoading && materialItems.length === 0 && selectedCustomerId && selectedWorkerId && selectedProcessId && (
        <Card>
          <CardContent className="p-8 text-center">
            <PackageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Issued Materials Found</h3>
            <p className="text-gray-600 mb-4">
              No materials have been issued for the selected worker and process combination.
            </p>
            <Button variant="outline" onClick={loadIssuedMaterials}>
              Refresh
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      {materialItems.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Label htmlFor="notes">Receipt Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any notes about this receipt..."
                  value={form.watch('notes') || ''}
                  onChange={(e) => form.setValue('notes', e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex items-center gap-4 ml-6">
                <Button
                  variant="outline"
                  onClick={() => handleSaveDraft()}
                  disabled={isSubmitting}
                >
                  Save Draft
                </Button>
                <Button
                  onClick={() => handleCompleteReceipt()}
                  disabled={isSubmitting || materialItems.filter(item => item.status === 'completed').length === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? 'Processing...' : 'Complete Receipt'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Helper functions
  function handleBatchReceiptAsIssued() {
    const updatedItems = materialItems.map(item => {
      if (selectedItems.includes(item.id)) {
        return {
          ...item,
          receivedWeight: item.issuedWeight,
          receivedQuantity: item.issuedQuantity,
          lossPercentage: 0,
          status: 'completed' as const
        };
      }
      return item;
    });
    setMaterialItems(updatedItems);
    toast.success(`${selectedItems.length} items received as issued`);
  }

  function handleBatchDustCollection() {
    // This would open a dust collection dialog
    toast.info('Batch dust collection feature coming soon');
  }

  function handleSaveDraft() {
    toast.success('Receipt saved as draft');
  }

  async function handleCompleteReceipt() {
    try {
      setIsSubmitting(true);

      const completedItems = materialItems.filter(item => item.status === 'completed');

      if (completedItems.length === 0) {
        toast.error('No items marked as completed');
        return;
      }

      // Process each completed item
      for (const item of completedItems) {
        if (item.type === 'stone') {
          await receiveStones({
            transaction_id: item.id,
            stones_received: [{
              stone_inventory_id: item.id,
              returned_quantity: item.receivedQuantity || 0,
              returned_weight_carats: item.receivedWeight,
              condition: item.lossPercentage > 10 ? 'damaged' : 'good',
              notes: item.lossPercentage > 5 ? 'Excessive loss detected' : undefined
            }],
            received_by: 'current_user', // TODO: Get from auth context
            notes: form.getValues('notes')
          });
        } else if (item.type === 'finding') {
          await receiveFindings({
            transaction_id: item.id,
            finding_id: item.id,
            received_gross_weight_grams: item.receivedWeight,
            received_net_weight_grams: item.receivedWeight, // Assuming same for now
            condition: item.lossPercentage > 5 ? 'damaged' : 'good',
            received_by: 'current_user',
            notes: form.getValues('notes')
          });
        } else if (item.type === 'metal') {
          await receiveMaterial({
            transaction_id: item.id,
            received_weight_grams: item.receivedWeight,
            dust_collected_grams: item.dustCollected,
            loss_percentage: item.lossPercentage,
            received_by: 'current_user',
            notes: form.getValues('notes')
          });
        }
      }

      toast.success(`Successfully received ${completedItems.length} items`);

      // Reset form and reload materials
      form.reset();
      setMaterialItems([]);
      setSelectedItems([]);

      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error completing receipt:', error);
      toast.error('Failed to complete receipt');
    } finally {
      setIsSubmitting(false);
    }
  }
}
